<svg width="539" height="601" viewBox="0 0 539 601" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_3_106)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M456.946 428.768C461.201 483.418 461.201 509.036 461.201 537H334.756C334.756 530.909 334.865 525.337 334.975 519.687C335.317 502.123 335.674 483.807 332.828 446.819C329.067 392.667 305.748 380.634 262.871 380.634H224.883H64V282.109H268.889C323.049 282.109 350.13 265.633 350.13 222.011C350.13 183.654 323.049 160.41 268.889 160.41H64V64H291.456C414.069 64 475 121.912 475 214.42C475 283.613 432.123 328.739 374.201 336.26C423.096 346.037 451.681 373.865 456.946 428.768Z" fill="#E8F2FF"/>
<path d="M64 537V463.553H197.697C220.029 463.553 224.878 480.116 224.878 489.994V537H64Z" fill="#E8F2FF"/>
</g>
<defs>
<filter id="filter0_dd_3_106" x="0" y="0" width="539" height="601" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="28"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.223529 0 0 0 0 0.572549 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_106"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="32"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.223529 0 0 0 0 0.572549 0 0 0 0 1 0 0 0 0.9 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_3_106" result="effect2_dropShadow_3_106"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_3_106" result="shape"/>
</filter>
</defs>
</svg>
