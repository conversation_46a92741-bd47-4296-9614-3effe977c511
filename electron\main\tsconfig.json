{
  "include": ["."],
  "compilerOptions": {
    "lib": ["ESNext"],
    "jsx": "preserve",
    "target": "ESNext",
    "noEmit": true,
    "skipLibCheck": true,
    "useDefineForClassFields": true,

    /* modules */
    "moduleResolution": "Bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "module": "ESNext",
    "isolatedModules": true,
    "emitDeclarationOnly": true,
    "declaration": true,
    "declarationDir": "./dist",

    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "verbatimModuleSyntax": true,
    "forceConsistentCasingInFileNames": true
  }
}
