name: <PERSON><PERSON><PERSON>ull Request
on:
  pull_request_target:
    types: [opened, reopened, edited, synchronize]
permissions:
  pull-requests: read
jobs:
  main:
    name: Validate PR Title
    runs-on: ubuntu-latest
    steps:
      # https://github.com/amannn/action-semantic-pull-request/releases/tag/v5.5.3
      - uses: amannn/action-semantic-pull-request@0723387faaf9b38adef4775cd42cfd5155ed6017
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          subjectPattern: ^(?![A-Z]).+$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            doesn't start with an uppercase character.
          types: |
            fix
            feat
            chore
            build
            ci
            perf
            docs
            refactor
            revert
            test
