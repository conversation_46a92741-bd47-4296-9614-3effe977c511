.diff-panel-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.diff-panel-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.diff-panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.diff-panel-content::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 4px;
  border: 2px solid transparent;
}

.diff-panel-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

/* Hide scrollbar for the left panel when not hovered */
.diff-panel:not(:hover) .diff-panel-content::-webkit-scrollbar {
  display: none;
}

.diff-panel:not(:hover) .diff-panel-content {
  scrollbar-width: none;
}

/* Estilos para as linhas de diff */
.diff-block-added {
  @apply bg-green-500/20 border-l-4 border-green-500;
}

.diff-block-removed {
  @apply bg-red-500/20 border-l-4 border-red-500;
}

/* Melhorar contraste para mudanças */
.diff-panel-content .group:hover .diff-block-added {
  @apply bg-green-500/30;
}

.diff-panel-content .group:hover .diff-block-removed {
  @apply bg-red-500/30;
}

/* Estilos unificados para ambas as visualizações */
.diff-line {
  @apply flex group min-w-fit transition-colors duration-150;
}

.diff-line-number {
  @apply w-12 shrink-0 pl-2 py-0.5 text-left font-mono text-bolt-elements-textTertiary border-r border-bolt-elements-borderColor bg-bolt-elements-background-depth-1;
}

.diff-line-content {
  @apply px-4 py-0.5 font-mono whitespace-pre flex-1 group-hover:bg-bolt-elements-background-depth-2 text-bolt-elements-textPrimary;
}

/* Cores específicas para adições/remoções */
.diff-added {
  @apply bg-green-500/20 border-l-4 border-green-500;
}

.diff-removed {
  @apply bg-red-500/20 border-l-4 border-red-500;
}
