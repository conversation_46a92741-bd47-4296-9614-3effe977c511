name: 'Bug report'
description: Create a report to help us improve
body:
  - type: markdown
    attributes:
      value: |
        Thank you for reporting an issue :pray:.

        This issue tracker is for bugs and issues found with [Bolt.diy](https://bolt.diy).
        If you experience issues related to WebContainer, please file an issue in the official [StackBlitz WebContainer repo](https://github.com/stackblitz/webcontainer-core).

        The more information you fill in, the better we can help you.
  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: Provide a clear and concise description of what you're running into.
    validations:
      required: true
  - type: input
    id: link
    attributes:
      label: Link to the Bolt URL that caused the error
      description: Please do not delete it after reporting!
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce
      description: Describe the steps we have to take to reproduce the behavior.
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: Provide a clear and concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    id: screenshots
    attributes:
      label: Screen Recording / Screenshot
      description: If applicable, **please include a screen recording** (preferably) or screenshot showcasing the issue. This will assist us in resolving your issue <u>quickly</u>.
  - type: textarea
    id: platform
    attributes:
      label: Platform
      value: |
        - OS: [e.g. macOS, Windows, Linux]
        - Browser: [e.g. Chrome, Safari, Firefox]
        - Version: [e.g. 91.1]
  - type: input
    id: provider
    attributes:
      label: Provider Used
      description: Tell us the provider you are using.
  - type: input
    id: model
    attributes:
      label: Model Used
      description: Tell us the model you are using.
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here.
