logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

.vscode/*
.vscode/launch.json
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

/.history
/.cache
/build
functions/build/
.env.local
.env
.dev.vars
*.vars
.wrangler
_worker.bundle

Modelfile
modelfiles

# docs ignore
site

# commit file ignore
app/commit.json
changelogUI.md
docs/instructions/Roadmap.md
.cursorrules
*.md
.qodo
